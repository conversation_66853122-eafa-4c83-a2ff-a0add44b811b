/* Modern Job Cards Styling */

/* Jobs list container - 2x2 Grid */
.jobs-list {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 20px !important;
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 0 !important;
    align-items: stretch !important;
    background-color: transparent !important;
}

/* Modern job card styling - Grid optimized */
.modern-job-card {
    background-color: #fff;
    border-radius: 16px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    display: flex !important;
    flex-direction: column !important;
    position: relative;
    width: 100% !important;
    height: auto !important;
    margin: 0 !important;
    padding: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.modern-job-card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

/* Card header */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px 24px 16px;
    background-color: #fff;
    border-bottom: 1px solid #f1f3f4;
}

.job-title-main {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    line-height: 1.4;
}

.posted-date {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

/* Card body */
.card-body {
    padding: 0 24px 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

/* Client profile section */
.client-profile-section {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f8fafc;
    border-radius: 12px;
    border-left: 4px solid #cd208b;
}

.client-avatar-container {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 2px solid #e5e7eb;
    overflow: hidden;
    flex-shrink: 0;
}

.client-avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.client-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.client-name-section {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.client-business-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.client-rating {
    display: flex;
    align-items: center;
    gap: 8px;
}

.rating-number {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
}

.rating-stars {
    display: flex;
    gap: 2px;
}

.rating-stars i {
    font-size: 0.75rem;
    color: #fbbf24;
}

.client-specialty {
    font-size: 0.875rem;
    color: #6b7280;
    font-style: italic;
}

.client-location-section {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.875rem;
    color: #6b7280;
}

.client-location-section i {
    color: #9ca3af;
}

.linkedin-link {
    align-self: flex-start;
}

.linkedin-btn {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    background-color: #0077b5;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.linkedin-btn:hover {
    background-color: #005885;
    color: white;
    text-decoration: none;
}

/* Job description section */
.job-description-section {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f9fafb;
    border-radius: 8px;
    border-left: 3px solid #e5e7eb;
}

.job-description-section {
    font-size: 0.9rem;
    color: #4b5563;
    line-height: 1.6;
}

/* Job details grid */
.job-details-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background-color: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    font-size: 0.875rem;
    color: #374151;
}

.detail-item i {
    color: #004aad;
    width: 16px;
    text-align: center;
    flex-shrink: 0;
}

.detail-item span {
    font-weight: 500;
}

.budget-type-item {
    grid-column: 1 / -1;
    background-color: #f0f7ff;
    border-color: #004aad;
    border-left: 4px solid #004aad;
}

/* Card footer */
.card-footer {
    padding: 16px 24px;
    border-top: 1px solid #f1f3f4;
    background-color: #fff;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.view-details-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    background-color: #cd208b;
    color: white;
    font-weight: 600;
    border-radius: 8px;
    text-decoration: none;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.view-details-btn:hover {
    background-color: #b91c7a;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(205, 32, 139, 0.3);
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .jobs-list {
        gap: 16px !important;
    }
}

@media (max-width: 768px) {
    .jobs-list {
        grid-template-columns: 1fr !important;
        gap: 16px !important;
    }

    .card-header {
        padding: 16px 20px 12px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .job-title-main {
        font-size: 1.125rem;
    }

    .card-body {
        padding: 0 20px 16px;
    }

    .client-profile-section {
        padding: 12px;
        gap: 12px;
    }

    .client-avatar-container {
        width: 50px;
        height: 50px;
    }

    .client-business-name {
        font-size: 1rem;
    }

    .job-details-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .detail-item {
        padding: 10px;
    }

    .card-footer {
        padding: 12px 20px;
    }

    .view-details-btn {
        padding: 8px 16px;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .jobs-list {
        grid-template-columns: 1fr !important;
        gap: 12px !important;
    }

    .modern-job-card {
        border-radius: 12px;
    }

    .card-header {
        padding: 12px 16px 8px;
    }

    .job-title-main {
        font-size: 1rem;
    }

    .card-body {
        padding: 0 16px 12px;
    }

    .client-profile-section {
        padding: 10px;
        gap: 10px;
        flex-direction: column;
        text-align: center;
    }

    .client-avatar-container {
        width: 45px;
        height: 45px;
        align-self: center;
    }

    .client-details {
        align-items: center;
    }

    .card-footer {
        padding: 10px 16px;
    }
}
